<template>
    <view class="example-page" :style="{ 'padding-top': pagePaddingTop + 'rpx' }">
        <!-- 自定义导航栏 -->
        <CustomBar title="精选分类示例" :isShowCart="false" />
        
        <!-- 页面内容 -->
        <view class="page-content">
            <!-- 基础使用示例 -->
            <view class="section">
                <FeaturedCategory 
                    :categoryList="defaultCategories"
                    @category-click="handleCategoryClick"
                />
            </view>

            <!-- 自定义标题示例 -->
            <view class="section">
                <FeaturedCategory 
                    title="热门推荐"
                    :categoryList="hotCategories"
                    @category-click="handleCategoryClick"
                />
            </view>

            <!-- 4列布局示例 -->
            <view class="section">
                <FeaturedCategory 
                    title="全部分类"
                    :columns="4"
                    :categoryList="allCategories"
                    @category-click="handleCategoryClick"
                />
            </view>
        </view>
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { AppModule } from '@/store/modules/app';
import CustomBar from '@/components/CustomBar/CustomBar.vue';
import FeaturedCategory from './FeaturedCategory.vue';
import { CategoryItem, CategoryClickEvent } from './types';

@Component({
    components: {
        CustomBar,
        FeaturedCategory
    }
})
export default class CategoryExample extends Vue {
    // 获取页面顶部padding
    get pagePaddingTop(): number {
        return AppModule.pagePaddingTop;
    }

    // 默认分类数据
    public defaultCategories: CategoryItem[] = [
        {
            id: 1,
            name: '扫地机',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
            type: 'product',
            tid: '11'
        },
        {
            id: 2,
            name: '洗地机',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
            type: 'product',
            tid: '10'
        },
        {
            id: 3,
            name: '吸尘器',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
            type: 'product',
            tid: '12'
        },
        {
            id: 4,
            name: '吹风机',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
            type: 'product',
            tid: '13'
        },
        {
            id: 5,
            name: '空气净化器',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
            type: 'product',
            tid: '14'
        },
        {
            id: 6,
            name: '除螨仪',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
            type: 'product',
            tid: '16'
        }
    ];

    // 热门分类数据
    public hotCategories: CategoryItem[] = [
        {
            id: 101,
            name: '新品上市',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
            type: 'special',
            url: '/pagesA/brandType/brandType?tid=1'
        },
        {
            id: 102,
            name: '热销爆款',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
            type: 'special',
            url: '/pagesA/brandType/brandType?tid=2'
        },
        {
            id: 103,
            name: '限时优惠',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
            type: 'special',
            url: '/pagesA/brandType/brandType?tid=3'
        },
        {
            id: 104,
            name: '配件专区',
            image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
            type: 'special',
            url: '/pagesA/brandType/brandType?tid=20'
        }
    ];

    // 全部分类数据（4列布局）
    public allCategories: CategoryItem[] = [];

    // 在created生命周期中初始化数据
    created(): void {
        this.allCategories = [
            ...this.defaultCategories,
            {
                id: 7,
                name: '加湿器',
                image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d6c31d872040023688.png',
                type: 'product',
                tid: '17'
            },
            {
                id: 8,
                name: '配件',
                image: 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/687e5d74d3d1e8680011636.png',
                type: 'product',
                tid: '20'
            }
        ];
    }

    // 处理分类点击事件
    handleCategoryClick(event: CategoryClickEvent): void {
        const { item, index } = event;
        
        console.log('点击了分类:', {
            name: item.name,
            id: item.id,
            position: index
        });

        // 可以在这里添加自定义的处理逻辑
        uni.showToast({
            title: `点击了${item.name}`,
            icon: 'none',
            duration: 1500
        });
    }

    // 页面生命周期
    onLoad(): void {
        console.log('分类示例页面加载完成');
    }
}
</script>

<style lang="scss" scoped>
.example-page {
    min-height: 100vh;
    background: $fill-color-bg-gray;

    .page-content {
        padding: 16rpx;

        .section {
            margin-bottom: 32rpx;
        }
    }
}
</style>
