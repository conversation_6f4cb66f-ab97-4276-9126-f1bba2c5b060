<template>
    <FeaturedCategory 
        :categoryList="categories"
        @category-click="handleCategoryClick"
    />
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { FeaturedCategory, CategoryClickEvent } from '@/pagesC/category/components';

@Component({
    components: { FeaturedCategory }
})
export default class YourPage extends Vue {
    handleCategoryClick(event: CategoryClickEvent) {
        console.log('点击了分类:', event.item.name);
    }
}
</script>