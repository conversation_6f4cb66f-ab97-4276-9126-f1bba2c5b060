<template>
    <view class="prod-item u-flex u-col-top u-row-left" :class="{ 'prod-item-b': target === 'product' }">
        <image class="prod-img" :src="getThumbImg(prodInfo.image, 150)" mode="" />

        <view class="prod-info u-flex-1">
            <view class="name">{{ prodInfo.name }}</view>
            <view class="sn">产品序列号: {{ prodInfo.sn }}</view>
            <view class="date">注册时间: {{ prodInfo.create_time | date('yyyy.mm.dd') }}</view>
        </view>
    </view>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import Utils from '@/common/Utils';

@Component({})
export default class ProdItem extends Vue {
    @Prop()
    readonly target: 'serve' | 'product' = 'serve';

    @Prop()
    readonly prodInfo;

    getThumbImg(str, size) {
        return Utils.getThumbImg(str, size);
    }
}
</script>
<style lang="scss">
.prod-item {
    width: 100%;
    height: 180rpx;
    padding: 15rpx;
    background: #f8f8f8;
    border-radius: 8rpx;

    &.prod-item-b {
        background: #ffffff;
    }

    .prod-img {
        width: 150rpx;
        height: 150rpx;
        margin-right: 24rpx;
    }

    .prod-info {
        height: 100%;

        .name {
            padding-top: 10rpx;
            width: 100%;
            font-size: 28rpx;
            color: #222222;
            line-height: 38rpx;
            margin-bottom: 10rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .sn,
        .date {
            width: 100%;
            font-size: 24rpx;
            color: rgba(34, 34, 34, 0.5);
            line-height: 34rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 8rpx;
        }
    }
}
</style>
