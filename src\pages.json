{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/indexMova",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/indexBrand",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/indexBrandB",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/indexBrandC",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/indexBlack",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/shop/shop",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/mine/mine",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/serve/serve",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/phoneAuth/phoneAuth",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/goodsDetail/goodsDetail",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/myProduct/myProduct",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/paySuccess/paySuccess",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/webView/webView",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/contents",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/coin/coin",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/coinDetail/coinDetail",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/coin/coinExchange",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/luckyDraw/luckyDraw",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/luckyRecord/luckyRecord",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/officialAccount",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/videoLiveStreaming",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/dtdService/dtdService",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/vipCenter/vipCenter",
      "enableShareAppMessage": true,
      "enableShareTimeline": true,
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/order/order",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/refund/refund",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/address/address-list",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/orderDetail/orderDetail",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/refundDetail/refundDetail",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/friendsList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/components/myFriendList/myFriendList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/components/addressBook/addressBook",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/components/attention/attention",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/PersonalCode",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/contents/PersonCodeEnter",
      "style": {
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pagesA",
      "pages": [
        {
          "path": "renew/logistics",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "renew/confirmResults",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "renew/tradeInDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "renew/evaluate",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "renew/chooseOld",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "renew/renewList",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "renew/renew",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pointDetails/pointDetails",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "productRegister/productRegister",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "productRegisterTip/productRegisterTip",
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 0
          }
        },
        {
          "path": "productEditInfo/productEditInfo",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "settlement/settlement",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "address/address-list",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "address/add-address",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/setting",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/privacy",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/protocol",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/privacy-update",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/thirdpart-info",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/permission",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/privacy-summary",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/personal-info",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/rules",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/tradeIn",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/equity",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/explain",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/sview",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sharePage/sharePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "coupon/coupon",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "coupon/share",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "coupon/records",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "daliyCheckIn/daliyCheckIn",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "missionCenter/missionCenter",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "memberShip/memberShip",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "dreamePoint/dreamePoint",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "point/point",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "point/inner_share_undertaking_page",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "point/new_point",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "point/shop_point",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "point/pointRules",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "point/share_undertaking_page",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "explainRules/explainRules",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "couponReceive/couponReceive",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search/search",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "preSaleRule/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "tailSettlement/settlement",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "partsCate/partsCate",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "product/comparison",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "product/comparisionDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "welfareFestivalHomePage/welfareFestivalHomePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "birthday/birthday",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "exploreLife/exploreLife",
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 200
          }
        },
        {
          "path": "guidance/guidance",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "recommend/recommend",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "recommend/record",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "logistics/logistics",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "cart/cart",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "wish/wish",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "brandType/brandType",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "recommendFriend/recommendFriend",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "exploreDetail/exploreDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "userInfo/userInfo",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "userInfo/edit-userInfo",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "order/order",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orderDetail/orderDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refund/refund",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refundChoice/refundChoice",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refundDetail/refundDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "exploreCancel/exploreCancel",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "exploreRorCdetail/exploreRorCdetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "nearShop/nearShop",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "productInformation/productInformation",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "warrantyDescription/warrantyDescription",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "verifyInformation/verifyInformation",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "giveExample/giveExample",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "exploreBooking/exploreBooking",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "dtdService/submitSuccess",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "dtdService/workOrder",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "cashier/cashier",
          "style": {
            "navigationStyle": "custom"
          }
        },
        // #ifdef H5
        {
          "path": "404/404",
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 0
          }
        },
        {
          "path": "exploreBookingMova/exploreBookingMova",
          "style": {
            "navigationStyle": "custom"
          }
        },
        // #endif
        {
          "path": "dtdService/areaSearch",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "innerBuyShop/shop",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "contentDetails/contentDetails",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ],
      "plugins": {
        "live-player-plugin": {
          "version": "1.3.5",
          "provider": "wx2b03c6e691cd7370"
        }
      }
    },
    {
      "root": "pagesB",
      "pages": [
        {
          "path": "deviceList/deviceList",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "doubleEleven/doubleEleven",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "memberActivity/memberActivity",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 0
          }
        },
        {
          "path": "deviceList/message",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "deviceList/offline",
          "style": {
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "superApp/index",
          "style": {
            "navigationStyle": "custom",
            "backgroundColor": "#000000"
          }
        },
        {
          "path": "superApp/topicList",
          "style": {
            "navigationStyle": "custom",
            "backgroundColor": "#000000"
          }
        },
        {
          "path": "giftCard/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "giftCard/searchCodeNote",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "giftCard/getCard",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "giftCard/shareRecord",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "giftCard/bindCard",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "evaluate/index",
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 0
          }
        },
        {
          "path": "shopFestival/shopFestival",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 0
          }
        },
        {
          "path": "groupGoods/groupGoods",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "groupGoodsSearch/index",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "groupingGoodsSearch/index",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "myGroupList/index",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inGroupPurchase/index",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inGroupPurchase/indexNew",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inGroupPurchase/guidPage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "useFirst/poster",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "evaluate/report",
          "style": {
            "navigationStyle": "custom",
            "onReachBottomDistance": 0
          }
        },
        {
          "path": "evaluate/evaluateList",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "evaluate/myEvaluate",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "evaluate/evaluateDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "evaluate/rulePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "videoPlayer/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inShop/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "nationalSubsidy/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inShop/smileMission/smileMission",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inShop/smilePoint/smilePoint",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inShop/smileRule/smileRule",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "inShop/recommend/record",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "goodsDetail/goodsDetail",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pagesC",
      "pages": [
        {
          "path": "pointPurchase/pointNewUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pointPurchase/pointAssistancePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "pointPurchase/pointPurchase",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "halfPrice/sharePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "halfPrice/halfPriceDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "halfPrice/halfPrice",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "halfPrice/inviteRecord",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "billionsOfShoppingGold/sharePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "billionsOfShoppingGold/billionsOfShoppingGoldDetail",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "billionsOfShoppingGold/billionsOfShoppingGold",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "billionsOfShoppingGold/inviteRecord",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "oneYuanFlashSale/newUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "oneYuanFlashSale/assistancePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "oneYuanFlashSale/oneYuanFlashSale",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "oneYuanFlashSale/oneYuanFlashSale",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "contentVideoDetails/contentVideoDetails",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTextStyle": "white"
          }
        },
        {
          "path": "selfCommunity/selfCommunity",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "selfCommunity/userList",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "selfCommunity/drafts",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search/search",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "contentDetails/contentDetails",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "contentReport/contentReport",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "createContent/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "createShop/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "shopStore/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "shopStore/orderDeatil",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "shopStore/shareProduct",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "shopProduct/shop",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "shopProduct/recommendationShop",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "position/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "userPermission/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "relatedTopic/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "myProduct/myProduct",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "dtdService/dtdService",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "paySuccess/paySuccess",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "serve/serve",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "midYearFestival/midYearFestival",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "lucky/lucky",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "selectMall/selectMall",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "xiaohongshuShow/index",
          "enableShareAppMessage": true,
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "jsfl/jsfl",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "jsfl/share_undertaking_page",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "jsfl/suchao_share",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "earnMoneySpend/inviteRecord",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "earnMoneySpend/sharePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "earnMoneySpend/earnMoneySpend",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "ambassador/login/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "ambassador/detail/index",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "ambassador/recommend/index",
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "ambassador/rule/index",
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "ambassador/invite/index",
          "enableShareTimeline": true,
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "earnMoneySpend/earnMoneySpend",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "earnMoneySpend/inviteRecord",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "offPurchase/offPurchase",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "offPurchase/offPurchaseNewUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "offPurchase/offPurchasePage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "eightDiscount/eightDiscount",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "eightDiscount/eightDiscountNewUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "eightDiscount/eightDiscountPage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sixDiscount/sixDiscount",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sixDiscount/sixDiscountNewUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sixDiscount/sixDiscountPage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "goldCoins/myGoldCoins",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "goldCoins/coinRecord",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "goldCoins/goldCoins",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "goldCoins/goldCoinsShare",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "goldCoins/drawPrize",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sixDiscount/sixDiscount",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sixDiscount/sixDiscountNewUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sixDiscount/sixDiscountPage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "threeDiscount/threeDiscount",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "threeDiscount/aThreeDiscount",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "threeDiscount/threeDiscountNewUser",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "threeDiscount/threeDiscountPage",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "qixiFestival/qixiFestival",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "rpxCalcMaxDeviceWidth": 499,
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": " ",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#FFFFFF",
    "navigationStyle": "custom",
    "usingComponents": {
      // #ifdef MP-WEIXIN
      "t-video": "plugin://t-video/t-video",
      "privacy": "/wxcomponents/privacy/privacy",
      // #endif
      "painter": "/wxcomponents/painter/painter"
    }
  },
  // #ifdef MP-WEIXIN
  "tabBar": {
    "color": "#777777",
    "selectedColor": "#AB8C5E",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "white",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "static/tabbar/unselected/home.png",
        "selectedIconPath": "static/tabbar/selected/home.png"
      },
      {
        "pagePath": "pages/contents/contents",
        "text": "社区",
        "iconPath": "static/tabbar/unselected/community.png",
        "selectedIconPath": "static/tabbar/selected/community.png"
      },
      {
        "pagePath": "pages/shop/shop",
        "text": "商城",
        "iconPath": "static/tabbar/unselected/mall.png",
        "selectedIconPath": "static/tabbar/selected/mall.png"
      },
      {
        "pagePath": "pages/vipCenter/vipCenter",
        "text": "会员",
        "iconPath": "static/tabbar/unselected/vip.png",
        "selectedIconPath": "static/tabbar/selected/vip.png"
      },
      {
        "pagePath": "pages/mine/mine",
        "text": "我的",
        "iconPath": "static/tabbar/unselected/mine.png",
        "selectedIconPath": "static/tabbar/selected/mine.png"
      }
    ]
  },
  // #endif
  "easycom": {
    "autoscan": true,
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
    "^z-(.*)": "@zebra-ui/swiper/components/z-$1/z-$1.vue"
  },
  "preloadRule": {
    "pagesB/goodsDetail/goodsDetail": {
      "network": "all",
      "packages": [
        "pagesA"
      ]
    },
    "pages/shop/shop": {
      "network": "all",
      "packages": [
        "pagesB"
      ]
    },
    "pages/index/index": {
      "network": "all",
      "packages": [
        "pagesB"
      ]
    }
  }
}