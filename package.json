{"name": "dreame", "version": "2.1.6", "private": true, "scripts": {"build:dreame-mp": "node bin/cli.js", "build:dreame-mp-uat": "node bin/cli_uat.js", "build:dreame-mp-pre": "node bin/cli_pre.js", "build:mp-weixin-auto": "node bin/cli_mp.js", "dev": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:h5-uat": "cross-env NODE_ENV=uat UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "postinstall": "patch-package"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-h5": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-360": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-alipay": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-baidu": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-qq": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-toutiao": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-vue": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-mp-weixin": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-quickapp-native": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-quickapp-webview": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-stat": "^2.0.0-v3-24020191018001", "@lucky-canvas/uni": "^0.0.14", "@scanapp/html5-qrcode": "0.0.9", "@types/weixin-app": "^2.9.4", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^7.0.0", "@vue/shared": "^3.2.47", "compressorjs": "^1.2.1", "core-js": "^3.29.1", "dayjs": "^1.11.13", "eslint-plugin-html": "^6.2.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "flyio": "^0.6.2", "hjson": "^3.2.2", "hls.js": "^1.6.7", "lint-staged": "^10.5.4", "lottie-miniprogram": "1.0.12", "lottie-web": "^5.12.2", "moment": "^2.29.4", "prettier": "^2.8.7", "qs": "^6.11.2", "regenerator-runtime": "^0.12.1", "sass": "^1.60.0", "tslib": "2.5.0", "uni-axios": "^1.0.5", "uview-ui": "2.0.35", "vconsole": "^3.15.1", "video.js": "^8.0.4", "vue": "^2.7.14", "vue-class-component": "^6.3.2", "vue-lazyload": "^1.3.5", "vue-property-decorator": "^8.0.0", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.2.0", "zip-local": "^0.3.5"}, "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@babel/plugin-syntax-typescript": "^7.21.4", "@babel/runtime": "~7.12.0", "@dcloudio/types": "2.0.22", "@dcloudio/uni-automator": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-cli-i18n": "^2.0.1-alpha-36920221121001", "@dcloudio/uni-cli-shared": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-i18n": "^2.0.1-alpha-36920221121001", "@dcloudio/uni-migration": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-template-compiler": "^2.0.0-alpha-33020211130001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-alpha-33020211130001", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-alpha-33020211130001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-alpha-33020211130001", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-alpha-33020211130001", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-alpha-33020211130001", "@types/lodash": "^4.14.192", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-typescript": "^4.5.19", "@vue/cli-service": "^4.5.19", "axios": "^0.21.4", "axios-miniprogram-adapter": "^0.3.5", "babel-plugin-import": "^1.13.6", "babel-plugin-transform-decorators": "^6.24.1", "cross-env": "^7.0.2", "eslint": "^7.32.0", "eslint-config-alloy": "^4.9.0", "eslint-plugin-vue": "^7.20.0", "html5-qrcode": "^2.3.7", "jest": "^25.4.0", "lodash": "^4.17.21", "mini-types": "^0.1.7", "miniprogram-api-typings": "^3.9.0", "mp-ci": "^1.2.1", "patch-package": "^8.0.0", "postcss-comment": "^2.0.0", "query-string": "^6.14.1", "sass-loader": "^8.0.2", "typescript": "^3.9.10", "vue-eslint-parser": "^7.11.0", "vue-template-compiler": "^2.7.14", "vuex-module-decorators": "^1.2.0"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node test/verifyCommit.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.vue": ["eslint"], "*.ts": ["eslint"], "*.html": ["prettier --write"]}}