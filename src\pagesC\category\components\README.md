# FeaturedCategory 精选分类组件

## 组件描述
一个用于展示精选商品分类的网格布局组件，支持3x2的网格展示，具有良好的交互效果和响应式适配。

## 功能特性
- ✅ 3x2网格布局展示
- ✅ 支持自定义分类数据
- ✅ 点击交互效果
- ✅ 响应式适配
- ✅ 埋点统计
- ✅ 懒加载图片
- ✅ TypeScript支持

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| categoryList | Array | 默认分类数据 | 分类列表数据 |
| title | String | '精选分类' | 组件标题 |
| columns | Number | 3 | 网格列数 |

### CategoryItem 数据结构
```typescript
interface CategoryItem {
    id: string | number;    // 分类ID
    name: string;           // 分类名称
    image: string;          // 分类图片URL
    url?: string;           // 跳转链接（可选）
    type?: string;          // 分类类型（可选）
    tid?: string;           // 商品分类ID（可选）
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| category-click | { item: CategoryItem, index: number } | 分类点击事件 |

## 使用示例

### 基础使用
```vue
<template>
    <view>
        <FeaturedCategory 
            :categoryList="categories"
            @category-click="handleCategoryClick"
        />
    </view>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import FeaturedCategory from './components/FeaturedCategory.vue';

@Component({
    components: {
        FeaturedCategory
    }
})
export default class CategoryPage extends Vue {
    // 自定义分类数据
    public categories = [
        {
            id: 1,
            name: '扫地机',
            image: 'https://example.com/image1.png',
            tid: '11'
        },
        {
            id: 2,
            name: '洗地机',
            image: 'https://example.com/image2.png',
            tid: '10'
        }
        // ... 更多分类
    ];

    // 处理分类点击
    handleCategoryClick({ item, index }) {
        console.log('点击了分类:', item.name, '位置:', index);
    }
}
</script>
```

### 自定义标题和列数
```vue
<FeaturedCategory 
    title="热门分类"
    :columns="4"
    :categoryList="categories"
/>
```

## 样式定制
组件使用了项目的设计系统变量，支持以下自定义：

```scss
// 自定义样式变量
$custom-bg-color: #f8f8f8;
$custom-text-color: #333;

.featured-category {
    background: $custom-bg-color;
    
    .category-title {
        color: $custom-text-color;
    }
}
```

## 注意事项
1. 图片建议使用CDN链接，支持懒加载
2. 分类数据建议为6个，保持3x2的完整布局
3. 组件会自动处理点击埋点统计
4. 支持通过tid跳转到商品分类页面
5. 响应式设计，在小屏设备上会自动调整尺寸
